{"rustc": 1842507548689473721, "features": "[\"compression\", \"shell-scope\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"shell-scope\"]", "target": 7777433936937008561, "profile": 2225463790103693989, "path": 4794730845362293504, "deps": [[5028953107375689561, "tauri_utils", false, 5221716416011103417], [5139814738173381871, "heck", false, 1494643690100153846], [7625640349194543331, "proc_macro2", false, 10622815138022508584], [11040296607758576099, "tauri_codegen", false, 6328221976682180029], [15627468545721021522, "quote", false, 17601707884782376052], [17610220194442039367, "syn", false, 4704077437328301982]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-fbb8aa123c34501b\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}