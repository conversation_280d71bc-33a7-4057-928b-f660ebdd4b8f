@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  @apply w-screen h-screen;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
}

body {
  background: radial-gradient(ellipse at top, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
              radial-gradient(ellipse at bottom, rgba(236, 72, 153, 0.1) 0%, transparent 50%),
              linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
}

/* Glassmorphism utilities */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

.neon-border {
  border: 1px solid transparent;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.3)) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

.bg-darken {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(139, 92, 246, 0.2);
}

.soure-item .source-codes {
  @apply pl-4 relative;
}

.source-item-active {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(236, 72, 153, 0.2));
  @apply text-white border-l-2 border-neon-purple;
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

.soure-item .source-codes:before {
  content: "";
  @apply absolute top-0 bottom-0 border-l border-dotted border-stone-500;
}

.inp {
  @apply block w-full rounded-md outline-none shadow-sm sm:text-sm;
  @apply text-white px-2 py-0.5;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.3);
  transition: all 0.3s ease;
}

.inp:focus {
  border-color: rgba(139, 92, 246, 0.6);
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
}

#titlebar {
  @apply flex items-center justify-between text-white pl-2;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(139, 92, 246, 0.2);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.titlebar-actions {
  @apply flex items-center;
}

.titlebar-icon {
  @apply py-0.5 cursor-pointer transition-all duration-300;
  width: 30px;
  text-align: center;
  cursor: pointer;
  border-radius: 4px;
}

.titlebar-icon:hover {
  background: rgba(139, 92, 246, 0.2);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.3);
}

#ttb-close:hover {
  background: rgba(239, 68, 68, 0.3);
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.5);
  @apply text-red-300;
}

#editor {
  padding-top: 27px;
}

.project-explorer {
  @apply w-full text-left uppercase text-gray-400 text-xs;
}

.code-structure {
  @apply px-2 overflow-y-auto;
  height: calc(100vh - 70px);
}

/* Modern Glassmorphic Scrollbar */

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.6), rgba(236, 72, 153, 0.6));
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.8), rgba(236, 72, 153, 0.8));
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.5);
}

/* Neon button effects */
.btn-neon {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-300;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(236, 72, 153, 0.2));
  border: 1px solid rgba(139, 92, 246, 0.3);
  color: white;
  position: relative;
  overflow: hidden;
}

.btn-neon:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(236, 72, 153, 0.4));
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.5);
  transform: translateY(-2px);
}

.btn-neon:active {
  transform: translateY(0);
}

/* Tab styling */
.tab-item {
  @apply transition-all duration-300;
  background: rgba(255, 255, 255, 0.05);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-item:hover {
  background: rgba(139, 92, 246, 0.1);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.2);
}

/* Code editor enhancements */
.cm-editor {
  background: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(139, 92, 246, 0.1);
  border-radius: 8px;
  margin: 1rem;
}

.cm-focused {
  outline: none !important;
  border-color: rgba(139, 92, 246, 0.3) !important;
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.2) !important;
}

/* Project explorer styling */
.project-explorer {
  @apply text-white text-sm font-medium;
  text-transform: none;
}

/* Enhanced hover effects */
.source-folder:hover,
.soure-item:hover {
  transform: translateX(4px);
}

/* Smooth transitions for all interactive elements */
* {
  transition: transform 0.2s ease, box-shadow 0.3s ease, background 0.3s ease, border-color 0.3s ease;
}

/* Custom selection colors */
::selection {
  background: rgba(139, 92, 246, 0.3);
  color: white;
}

::-moz-selection {
  background: rgba(139, 92, 246, 0.3);
  color: white;
}
