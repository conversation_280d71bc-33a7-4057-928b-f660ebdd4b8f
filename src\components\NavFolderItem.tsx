import { nanoid } from "nanoid";
import { useState } from "react";
import { readDirectory, writeFile } from "../helpers/filesys";
import { saveFileObject } from "../stores/file";
import { IFile } from "../types";
import NavFiles from "./NavFiles";

interface Props {
  file: IFile;
  active: boolean;
}
export default function NavFolderItem({ file, active }: Props) {
  const [files, setFiles] = useState<IFile[]>([])
  const [unfold, setUnfold] = useState(false)
  const [loaded, setLoaded] = useState(false)
  const [newFile, setNewFile] = useState(false)
  const [filename, setFilename] = useState('')

  const onShow = async (ev: React.MouseEvent<HTMLSpanElement>) => {
    ev.stopPropagation()

    if (loaded) {
      setUnfold(!unfold)
      return;
    }

    const entries = await readDirectory(file.path + '/')

    setLoaded(true)
    setFiles(entries)
    setUnfold(!unfold)
  }

  const onEnter = (key: string) => { 
    if (key === 'Escape') {
      setNewFile(false)
      setFilename('')
      return;
    }

    if (key !== 'Enter') return;

    const filePath = `${file.path}/${filename}`
    
    writeFile(filePath, '').then(() => {
      const id = nanoid();
      const newFile: IFile = {
        id,
        name: filename,
        path: filePath,
        kind: 'file'
      }

      saveFileObject(id, newFile)
      setFiles(prevEntries => [newFile, ...prevEntries])
      setNewFile(false)
      setFilename('')
    })
    
  }

  return <div className="soure-item">
    <div className={`source-folder flex items-center gap-3 px-3 py-2 mx-2 rounded-lg cursor-pointer transition-all duration-300 group ${
      active
        ? 'bg-gradient-to-r from-neon-purple/20 to-neon-pink/10 text-white'
        : 'text-gray-400 hover:text-white hover:bg-glass'
    }`}>
      <i className={`ri-folder-${unfold ? 'open' : 'fill'} text-yellow-400 group-hover:text-yellow-300 transition-colors duration-200`}></i>
      <div className="source-header flex items-center justify-between w-full">
        <span onClick={onShow} className="font-medium group-hover:text-neon-purple transition-colors duration-200">
          {file.name}
        </span>
        <i
          onClick={() => setNewFile(true)}
          className="ri-add-line opacity-0 group-hover:opacity-100 hover:text-neon-green hover:bg-neon-green/20 rounded p-1 transition-all duration-200"
        ></i>
      </div>
    </div>

    {newFile ? <div className="mx-4 flex items-center gap-2 p-3 glass rounded-lg border border-neon-purple/30">
      <i className="ri-file-edit-line text-neon-purple"></i>
      <input
        type="text"
        value={filename}
        onChange={(ev) => setFilename(ev.target.value)}
        onKeyUp={(ev) => onEnter(ev.key)}
        className="inp flex-1"
        placeholder="Enter filename..."
        autoFocus
      />
    </div> : null}

    <div className={`ml-4 ${unfold ? 'border-l border-neon-purple/20' : ''}`}>
      <NavFiles visible={unfold} files={files} />
    </div>
  </div>
}
