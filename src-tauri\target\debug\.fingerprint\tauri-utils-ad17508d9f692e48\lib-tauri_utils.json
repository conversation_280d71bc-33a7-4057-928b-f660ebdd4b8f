{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"glob\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 14523740934585808576, "profile": 15657897354478470176, "path": 1340090754815358193, "deps": [[316205485569158363, "semver", false, 17699501260026917801], [993458479869452362, "ctor", false, 14895368767528760625], [1507133372236518602, "url", false, 6099875740219888305], [1764276339024939380, "phf", false, 3043433968431894977], [2283771217451780507, "serde_with", false, 6674386389144450575], [2812371858713659968, "html5ever", false, 9850514674100050885], [4939350138249908675, "json_patch", false, 9414796503834919382], [5934792813861369232, "serde_json", false, 16057785001452611407], [6079186729485567678, "memchr", false, 14340484860713226479], [7653476968652377684, "windows", false, 598939402927828347], [12267776084724251296, "glob", false, 6597055342370388852], [12479191710976922219, "serde", false, 8084369360333306611], [13038499899892950383, "brotli", false, 17381491514828624978], [15436052395560729636, "kuchiki", false, 10262169115188426090], [16321613276811503781, "thiserror", false, 15386719116644835377], [17619999962773151335, "walkdir", false, 4356269139227674465]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-ad17508d9f692e48\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}