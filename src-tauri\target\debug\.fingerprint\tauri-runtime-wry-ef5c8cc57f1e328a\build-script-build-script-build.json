{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"macos-private-api\", \"objc-exception\", \"system-tray\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 15296445091233919070, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-ef5c8cc57f1e328a\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}