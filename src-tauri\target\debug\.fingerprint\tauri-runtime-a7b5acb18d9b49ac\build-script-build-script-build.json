{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 5001768865256210005, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-a7b5acb18d9b49ac\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}