import { useState } from "react";
import { appWindow } from "@tauri-apps/api/window";

export default function Titlebar() {
  const [isScaleup, setScaleup] = useState(false);
  const onMinimize = () => appWindow.minimize();
  const onScaleup = () => {
    appWindow.toggleMaximize();
    setScaleup(true);
  }

  const onScaledown = () => {
    appWindow.toggleMaximize();
    setScaleup(false);
  }

  const onClose = () => appWindow.close();

  return <div id="titlebar" data-tauri-drag-region className="glass-dark">
    <div className="flex items-center gap-2 pl-3">
      <div className="w-3 h-3 rounded-full bg-gradient-to-r from-neon-purple to-neon-pink animate-pulse-neon"></div>
      <span className="text-sm font-bold bg-gradient-to-r from-neon-purple to-neon-pink bg-clip-text text-transparent animate-glow">
        HUDITOR
      </span>
      <div className="ml-2 text-xs text-gray-400 opacity-70">
        Modern Code Editor
      </div>
    </div>

    <div className="titlebar-actions flex items-center">
      <i className="titlebar-icon ri-subtract-line hover:text-neon-blue" onClick={onMinimize}></i>

      {isScaleup ?
        <i className="titlebar-icon ri-file-copy-line hover:text-neon-cyan" onClick={onScaledown}></i> :
        <i onClick={onScaleup} className="titlebar-icon ri-stop-line hover:text-neon-green"></i>
      }

      <i id="ttb-close" className="titlebar-icon ri-close-fill" onClick={onClose}></i>
    </div>
  </div>
}
