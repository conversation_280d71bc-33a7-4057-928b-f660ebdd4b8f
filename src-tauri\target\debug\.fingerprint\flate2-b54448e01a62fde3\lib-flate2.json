{"rustc": 1842507548689473721, "features": "[\"default\", \"miniz_oxide\", \"rust_backend\"]", "declared_features": "[\"any_zlib\", \"cloudflare-zlib-sys\", \"cloudflare_zlib\", \"default\", \"libz-ng-sys\", \"libz-sys\", \"miniz-sys\", \"miniz_oxide\", \"rust_backend\", \"zlib\", \"zlib-ng\", \"zlib-ng-compat\"]", "target": 7395671289533959251, "profile": 15657897354478470176, "path": 9757858709858482679, "deps": [[8254265804561796823, "crc32fast", false, 12866701632683732591], [16580831604252893568, "miniz_oxide", false, 7855849953872969165]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\flate2-b54448e01a62fde3\\dep-lib-flate2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}