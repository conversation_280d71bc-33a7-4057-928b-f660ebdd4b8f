{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"compression\", \"glob\", \"proc-macro2\", \"quote\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"glob\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"toml\", \"walkdir\"]", "target": 14523740934585808576, "profile": 2225463790103693989, "path": 1340090754815358193, "deps": [[316205485569158363, "semver", false, 16545439175068551239], [993458479869452362, "ctor", false, 14895368767528760625], [1507133372236518602, "url", false, 6099875740219888305], [1764276339024939380, "phf", false, 3043433968431894977], [2283771217451780507, "serde_with", false, 6674386389144450575], [2812371858713659968, "html5ever", false, 13269837637998729462], [4939350138249908675, "json_patch", false, 6698306920584013270], [5934792813861369232, "serde_json", false, 16247389549714770133], [6079186729485567678, "memchr", false, 14340484860713226479], [7625640349194543331, "proc_macro2", false, 10622815138022508584], [7653476968652377684, "windows", false, 16627521092767069648], [12267776084724251296, "glob", false, 6597055342370388852], [12479191710976922219, "serde", false, 8084369360333306611], [13038499899892950383, "brotli", false, 17381491514828624978], [15436052395560729636, "kuchiki", false, 8852465440559208439], [15627468545721021522, "quote", false, 17601707884782376052], [16321613276811503781, "thiserror", false, 15386719116644835377], [17619999962773151335, "walkdir", false, 3630878855028694034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-7c38ba9653bd9f2d\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}