import CodeArea from "./components/CodeArea"
import Sidebar from "./components/Sidebar"
import Titlebar from "./components/Titlebar"
import { SourceProvider } from "./context/SourceContext"

export default function App() {
  return <div className="wrapper relative min-h-screen">
    {/* Animated background elements */}
    <div className="fixed inset-0 overflow-hidden pointer-events-none">
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-neon-purple/10 rounded-full blur-3xl animate-float"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-neon-pink/10 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
      <div className="absolute top-3/4 left-1/2 w-64 h-64 bg-neon-blue/10 rounded-full blur-3xl animate-float" style={{animationDelay: '2s'}}></div>
    </div>

    <Titlebar />
    <div id="editor" className="h-screen flex items-start overflow-hidden relative">
      <SourceProvider>
        <Sidebar />
        <CodeArea />
      </SourceProvider>
    </div>
  </div>
}


