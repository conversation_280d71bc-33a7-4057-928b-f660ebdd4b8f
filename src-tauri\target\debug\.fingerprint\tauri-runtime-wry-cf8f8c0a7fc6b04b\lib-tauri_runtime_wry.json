{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\", \"objc-exception\"]", "declared_features": "[\"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"macos-private-api\", \"objc-exception\", \"system-tray\"]", "target": 12286328633679254747, "profile": 15657897354478470176, "path": 3152774185787731311, "deps": [[4381063397040571828, "webview2_com", false, 17524134845070246676], [5028953107375689561, "tauri_utils", false, 5370285959481826727], [5285829931569896961, "wry", false, 8492993464178549642], [7653476968652377684, "windows", false, 598939402927828347], [10910351676432160967, "raw_window_handle", false, 15125246666940046532], [11892053608254638761, "tauri_runtime", false, 3761431201514082433], [12344803760052352817, "build_script_build", false, 194961783255925855], [13208667028893622512, "rand", false, 15273024932330238243], [16276023458712862899, "uuid", false, 8564838731854479472]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-cf8f8c0a7fc6b04b\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}