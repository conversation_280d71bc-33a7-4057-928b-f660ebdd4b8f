{"rustc": 1842507548689473721, "features": "[\"extern_crate_alloc\"]", "declared_features": "[\"aarch64_simd\", \"bytemuck_derive\", \"derive\", \"extern_crate_alloc\", \"extern_crate_std\", \"min_const_generics\", \"nightly_portable_simd\", \"unsound_ptr_pod_impl\", \"wasm_simd\", \"zeroable_maybe_uninit\"]", "target": 16196906332061044641, "profile": 15657897354478470176, "path": 13704233288591483969, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\bytemuck-3cbc077a7b8ab3e5\\dep-lib-bytemuck", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}