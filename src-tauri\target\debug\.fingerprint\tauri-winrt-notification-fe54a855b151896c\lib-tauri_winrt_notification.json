{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 16434929074262014398, "profile": 15657897354478470176, "path": 16952686820104780516, "deps": [[7653476968652377684, "windows", false, 598939402927828347], [11740926470601948399, "quick_xml", false, 7343779939438711175], [15567746551245677310, "strum", false, 11040606532621418625]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-winrt-notification-fe54a855b151896c\\dep-lib-tauri_winrt_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}