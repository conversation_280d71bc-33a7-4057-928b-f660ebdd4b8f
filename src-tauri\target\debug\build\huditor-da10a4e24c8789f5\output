cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rerun-if-changed=tauri.conf.json
cargo:rustc-cfg=desktop
cargo:rustc-cfg=dev
package.metadata does not exist
"C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.19041.0\\x64\\rc.exe"
"C:\\Program Files (x86)\\Windows Kits\\10\\bin\\10.0.26100.0\\x64\\rc.exe"
Selected RC path: 'C:\Program Files (x86)\Windows Kits\10\bin\10.0.26100.0\x64\rc.exe'
RC Output:
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.



------
RC Error:

------
cargo:rustc-link-search=native=C:\Users\<USER>\Desktop\huditor-main\src-tauri\target\debug\build\huditor-da10a4e24c8789f5\out
cargo:rustc-link-lib=dylib=resource
