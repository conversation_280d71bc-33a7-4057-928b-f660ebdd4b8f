{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"regex\", \"shell-scope\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"regex\", \"shell-scope\"]", "target": 3982420347758336739, "profile": 2225463790103693989, "path": 7756599408677224811, "deps": [[316205485569158363, "semver", false, 16545439175068551239], [874150583845701161, "png", false, 15956412116665343159], [3761666480252117995, "regex", false, 2486927843451261001], [4939350138249908675, "json_patch", false, 6698306920584013270], [5028953107375689561, "tauri_utils", false, 5221716416011103417], [5934792813861369232, "serde_json", false, 16247389549714770133], [7625640349194543331, "proc_macro2", false, 10622815138022508584], [8103030111375765111, "ico", false, 11413439421588978853], [12435202302586637291, "base64", false, 9546021644312374898], [12479191710976922219, "serde", false, 8084369360333306611], [13038499899892950383, "brotli", false, 17381491514828624978], [15627468545721021522, "quote", false, 17601707884782376052], [16276023458712862899, "uuid", false, 8564838731854479472], [16321613276811503781, "thiserror", false, 15386719116644835377], [16715618431955851775, "sha2", false, 765555269803264295], [17619999962773151335, "walkdir", false, 3630878855028694034]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-34dbcf91afb23671\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}