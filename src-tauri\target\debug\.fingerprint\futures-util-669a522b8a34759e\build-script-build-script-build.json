{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 17883862002600103897, "profile": 2225463790103693989, "path": 15100800024463186883, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-669a522b8a34759e\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}