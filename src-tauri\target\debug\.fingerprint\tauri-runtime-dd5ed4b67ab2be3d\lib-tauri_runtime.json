{"rustc": 1842507548689473721, "features": "[\"clipboard\", \"global-shortcut\"]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 13418454777403878664, "profile": 15657897354478470176, "path": 887030966059267866, "deps": [[598707355444551573, "infer", false, 3556072552819987530], [4381063397040571828, "webview2_com", false, 17524134845070246676], [5028953107375689561, "tauri_utils", false, 5370285959481826727], [5934792813861369232, "serde_json", false, 16057785001452611407], [7653476968652377684, "windows", false, 598939402927828347], [8866577183823226611, "http_range", false, 16569460656706332610], [9914303044191174054, "http", false, 17103104423891361321], [10910351676432160967, "raw_window_handle", false, 15125246666940046532], [11892053608254638761, "build_script_build", false, 5191715680935382536], [12479191710976922219, "serde", false, 8084369360333306611], [13208667028893622512, "rand", false, 15273024932330238243], [16276023458712862899, "uuid", false, 8564838731854479472], [16321613276811503781, "thiserror", false, 15386719116644835377]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-dd5ed4b67ab2be3d\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}