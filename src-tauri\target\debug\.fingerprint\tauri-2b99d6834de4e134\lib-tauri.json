{"rustc": 1842507548689473721, "features": "[\"api-all\", \"attohttpc\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"default\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-request\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os_info\", \"os_pipe\", \"path-all\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"protocol-all\", \"protocol-asset\", \"regex\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"tauri-runtime-wry\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-max-size\", \"window-set-min-size\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"attohttpc\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"infer\", \"isolation\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"system-tray\", \"tauri-runtime-wry\", \"time\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-max-size\", \"window-set-min-size\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 1305593064144852035, "profile": 15657897354478470176, "path": 14142635541644708839, "deps": [[316205485569158363, "semver", false, 17699501260026917801], [1507133372236518602, "url", false, 6099875740219888305], [2560440608430258298, "os_info", false, 13317132416003451883], [2938616610438976277, "futures_util", false, 4390506712091236493], [3073234229861826298, "build_script_build", false, 3059235513049432765], [3305663043119099774, "open", false, 1547332286751635847], [3761666480252117995, "regex", false, 2486927843451261001], [3952078167321401430, "tauri_macros", false, 14756780027330405031], [4381063397040571828, "webview2_com", false, 17524134845070246676], [4450062412064442726, "dirs_next", false, 1735733447452450528], [4919829919303820331, "serialize_to_javascript", false, 18167706958944230889], [5028953107375689561, "tauri_utils", false, 5370285959481826727], [5099504066399492044, "rfd", false, 17988279832554240111], [5118352927412202695, "percent_encoding", false, 12943564708569501510], [5909216664186256915, "os_pipe", false, 9524364051062590802], [5934792813861369232, "serde_json", false, 16057785001452611407], [6193038088885664399, "attohttpc", false, 5060650326335299060], [7216977700954388357, "encoding_rs", false, 13333516672032253478], [7537741750877035737, "ignore", false, 4637320900541006801], [7653476968652377684, "windows", false, 598939402927828347], [9914303044191174054, "http", false, 17103104423891361321], [10011325924590167423, "tempfile", false, 2183751388476417934], [10642096736294159704, "shared_child", false, 14378976280913128900], [10910351676432160967, "raw_window_handle", false, 15125246666940046532], [11892053608254638761, "tauri_runtime", false, 3761431201514082433], [12267776084724251296, "glob", false, 6597055342370388852], [12344803760052352817, "tauri_runtime_wry", false, 11802413513746342207], [12479191710976922219, "serde", false, 8084369360333306611], [13208667028893622512, "rand", false, 15273024932330238243], [13211958047774196934, "anyhow", false, 388306021535730711], [13807521765927245974, "tokio", false, 4685099311135256135], [13830659407736809578, "tar", false, 3792445547630875311], [16102220816499649880, "once_cell", false, 14539424370229730426], [16276023458712862899, "uuid", false, 8564838731854479472], [16321613276811503781, "thiserror", false, 15386719116644835377], [16494924123589645231, "flate2", false, 9295101648595390372], [17278893514130263345, "state", false, 2587789326250484080], [17479610736662497223, "notify_rust", false, 3490636572785240463], [17610467653877100105, "serde_repr", false, 13298055357239138824]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-2b99d6834de4e134\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}