{"rustc": 1842507548689473721, "features": "[\"compress\", \"default\", \"flate2\", \"form\", \"json\", \"native-tls\", \"serde\", \"serde_json\", \"serde_urlencoded\", \"tls\"]", "declared_features": "[\"base64\", \"basic-auth\", \"charsets\", \"compress\", \"default\", \"encoding_rs\", \"encoding_rs_io\", \"flate2\", \"form\", \"json\", \"mime\", \"multipart\", \"multipart-form\", \"native-tls\", \"rustls\", \"rustls-opt-dep\", \"serde\", \"serde_json\", \"serde_urlencoded\", \"tls\", \"tls-rustls\", \"tls-vendored\", \"webpki\", \"webpki-roots\"]", "target": 39871475727586841, "profile": 15657897354478470176, "path": 13046498123075422743, "deps": [[1507133372236518602, "url", false, 6099875740219888305], [5934792813861369232, "serde_json", false, 16057785001452611407], [9914303044191174054, "http", false, 17103104423891361321], [12479191710976922219, "serde", false, 8084369360333306611], [16095692449631247465, "native_tls", false, 2779116711961071046], [16494924123589645231, "flate2", false, 9295101648595390372], [16542808166767769916, "serde_urlencoded", false, 3220787448887262057], [17316484122781157649, "log", false, 5281187587265819790]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\attohttpc-1dffee6e82c51982\\dep-lib-attohttpc", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}