{"rustc": 1842507548689473721, "features": "[\"api-all\", \"attohttpc\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"default\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-request\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os_info\", \"os_pipe\", \"path-all\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"protocol-all\", \"protocol-asset\", \"regex\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"tauri-runtime-wry\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-max-size\", \"window-set-min-size\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"wry\"]", "declared_features": "[\"api-all\", \"attohttpc\", \"base64\", \"bytes\", \"clap\", \"cli\", \"clipboard\", \"clipboard-all\", \"clipboard-read-text\", \"clipboard-write-text\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dialog\", \"dialog-all\", \"dialog-ask\", \"dialog-confirm\", \"dialog-message\", \"dialog-open\", \"dialog-save\", \"dox\", \"fs-all\", \"fs-copy-file\", \"fs-create-dir\", \"fs-exists\", \"fs-extract-api\", \"fs-read-dir\", \"fs-read-file\", \"fs-remove-dir\", \"fs-remove-file\", \"fs-rename-file\", \"fs-write-file\", \"global-shortcut\", \"global-shortcut-all\", \"http-all\", \"http-api\", \"http-multipart\", \"http-request\", \"ico\", \"icon-ico\", \"icon-png\", \"infer\", \"isolation\", \"macos-private-api\", \"minisign-verify\", \"native-tls-vendored\", \"notification\", \"notification-all\", \"notify-rust\", \"objc-exception\", \"open\", \"os-all\", \"os_info\", \"os_pipe\", \"path-all\", \"png\", \"process-all\", \"process-command-api\", \"process-exit\", \"process-relaunch\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-all\", \"protocol-asset\", \"regex\", \"reqwest\", \"reqwest-client\", \"reqwest-native-tls-vendored\", \"rfd\", \"shared_child\", \"shell-all\", \"shell-execute\", \"shell-open\", \"shell-open-api\", \"shell-sidecar\", \"system-tray\", \"tauri-runtime-wry\", \"time\", \"updater\", \"win7-notifications\", \"window-all\", \"window-center\", \"window-close\", \"window-create\", \"window-data-url\", \"window-hide\", \"window-maximize\", \"window-minimize\", \"window-print\", \"window-request-user-attention\", \"window-set-always-on-top\", \"window-set-cursor-grab\", \"window-set-cursor-icon\", \"window-set-cursor-position\", \"window-set-cursor-visible\", \"window-set-decorations\", \"window-set-focus\", \"window-set-fullscreen\", \"window-set-icon\", \"window-set-max-size\", \"window-set-min-size\", \"window-set-position\", \"window-set-resizable\", \"window-set-size\", \"window-set-skip-taskbar\", \"window-set-title\", \"window-show\", \"window-start-dragging\", \"window-unmaximize\", \"window-unminimize\", \"windows7-compat\", \"wry\", \"zip\"]", "target": 5408242616063297496, "profile": 2225463790103693989, "path": 8409920462119558192, "deps": [[5139814738173381871, "heck", false, 1494643690100153846], [16102220816499649880, "once_cell", false, 14539424370229730426]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-e8367e5a8446d71d\\dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}