import { useRef } from "react"
import { convertFileSrc } from "@tauri-apps/api/tauri"

interface Props {
  path: string;
  active: boolean;
}

export default function PreviewImage({ path, active }: Props) {
  const imgRef = useRef<HTMLImageElement>(null)

  return <div className={`${active ? '' : 'hidden'} p-8 flex items-center justify-center min-h-full`}>
    <div className="glass rounded-2xl p-6 max-w-4xl max-h-full overflow-auto">
      <img
        ref={imgRef}
        src={convertFileSrc(path)}
        alt=""
        className="max-w-full h-auto rounded-lg shadow-glass border border-neon-purple/20"
      />
    </div>
  </div>
}

