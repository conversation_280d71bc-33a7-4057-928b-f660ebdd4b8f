{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"codegen\", \"config-json5\", \"config-toml\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 9570484432207332980, "profile": 2225463790103693989, "path": 3865883816543439864, "deps": [[316205485569158363, "semver", false, 16545439175068551239], [4894910450471987296, "cargo_toml", false, 13289739745914817765], [4939350138249908675, "json_patch", false, 6698306920584013270], [5028953107375689561, "tauri_utils", false, 5221716416011103417], [5139814738173381871, "heck", false, 1494643690100153846], [5934792813861369232, "serde_json", false, 16247389549714770133], [13211958047774196934, "anyhow", false, 388306021535730711], [13352475244373065756, "winres", false, 8346739761633038094]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-b29f2f6d8b47de03\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}