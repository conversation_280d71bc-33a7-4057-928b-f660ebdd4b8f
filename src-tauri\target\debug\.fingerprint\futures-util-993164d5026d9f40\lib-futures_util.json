{"rustc": 1842507548689473721, "features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"default\", \"futures-macro\", \"slab\", \"std\"]", "declared_features": "[\"alloc\", \"async-await\", \"async-await-macro\", \"bilock\", \"cfg-target-has-atomic\", \"channel\", \"compat\", \"default\", \"futures-channel\", \"futures-io\", \"futures-macro\", \"futures-sink\", \"futures_01\", \"io\", \"io-compat\", \"memchr\", \"sink\", \"slab\", \"std\", \"tokio-io\", \"unstable\", \"write-all-vectored\"]", "target": 6953988541840603879, "profile": 15657897354478470176, "path": 16917141895525502850, "deps": [[1615478164327904835, "pin_utils", false, 4068431111024179729], [2938616610438976277, "build_script_build", false, 15920297958076907031], [9649989619643071079, "futures_macro", false, 6367149710453176446], [11649779934342750820, "futures_core", false, 10092043758421983091], [12057736261416622125, "futures_task", false, 12501112924484931737], [14837160076977757511, "slab", false, 13011029500758237802], [15470534839312576504, "pin_project_lite", false, 5352856086339676075]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\futures-util-993164d5026d9f40\\dep-lib-futures_util", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}