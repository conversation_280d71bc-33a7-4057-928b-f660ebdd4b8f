import { useState } from "react";
import { IFile } from "../types";
import { open } from "@tauri-apps/api/dialog";
import NavFiles from "./NavFiles";
import { readDirectory } from "../helpers/filesys";

export default function Sidebar() {
  const [projectName, setProjectName] = useState("");
  const [files, setFiles] = useState<IFile[]>([]);

  const loadFile = async () => {
    const selected = await open({
      directory: true
    })

    if (!selected) return;

    setProjectName(selected as string)
    readDirectory(selected + '/').then(files => {
      console.log(files)
      setFiles(files)
    })
  }

  return <aside id="sidebar" className="w-60 shrink-0 h-full bg-darken relative">
    <div className="absolute inset-0 bg-gradient-to-b from-neon-purple/10 via-transparent to-neon-pink/10 pointer-events-none"></div>

    <div className="sidebar-header flex items-center justify-between p-4 py-3 relative z-10">
      <button
        className="btn-neon text-sm flex items-center gap-2 group"
        onClick={loadFile}
      >
        <i className="ri-folder-open-line group-hover:animate-float"></i>
        <span className="font-medium">Explorer</span>
      </button>

      {projectName && (
        <div className="flex flex-col items-end">
          <span className="text-xs text-neon-purple font-medium">PROJECT</span>
          <span className="project-name whitespace-nowrap text-white text-xs font-mono bg-glass px-2 py-1 rounded">
            {projectName.split('/').pop() || projectName.split('\\').pop()}
          </span>
        </div>
      )}
    </div>

    <div className="code-structure relative z-10">
      <NavFiles visible={true} files={files}/>
    </div>
  </aside>
}
