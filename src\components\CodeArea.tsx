import { IFile } from "../types"
import { useSource } from "../context/SourceContext"
import { getFileObject } from "../stores/file"
import FileIcon from "./FileIcon"
import useHorizontalScroll from "../helpers/useHorizontalScroll"
import PreviewImage from "./PreviewImage"
import CodeEditor from "./CodeEditor"

export default function CodeArea() {
  const { opened, selected, setSelect, delOpenedFile } = useSource()
  const scrollRef = useHorizontalScroll()
  const onSelectItem = (id: string) => {
    setSelect(id)
  }

  const isImage = (name: string) => {
    return ['.png', '.gif', '.jpeg', '.jpg', '.bmp'].some(ext => name.lastIndexOf(ext) !== -1) 
  }

  const close = (ev: React.MouseEvent<HTMLElement, MouseEvent>, id: string) => {
    ev.stopPropagation()
    delOpenedFile(id)
  }

  return <div id="code-area" className="w-full h-full relative">
    <div className="absolute inset-0 bg-gradient-to-br from-primary via-secondary to-accent"></div>

    <div ref={scrollRef} className="code-tab-items flex items-center glass-dark border-b border-neon-purple/30 overflow-x-auto relative z-10">
      {opened.map(item => {
        const file = getFileObject(item) as IFile;
        const active = selected === item

        return <div
          onClick={() => onSelectItem(file.id)}
          className={`tab-item shrink-0 px-4 py-2 cursor-pointer flex items-center gap-2 relative group ${
            active
              ? 'bg-gradient-to-r from-neon-purple/20 to-neon-pink/20 text-white border-b-2 border-neon-purple'
              : 'text-gray-400 hover:text-white hover:bg-glass'
          }`}
          key={item}
        >
          <FileIcon name={file.name} size="sm" />
          <span className="font-medium">{file.name}</span>
          <i
            onClick={(ev) => close(ev, item)}
            className="ri-close-line hover:text-red-400 hover:bg-red-500/20 rounded p-1 transition-all duration-200 opacity-0 group-hover:opacity-100"
          ></i>

          {active && (
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-neon-purple to-neon-pink"></div>
          )}
        </div>
      })}
    </div>

    <div className="code-contents relative z-10">
      {opened.map(item => {
        const file = getFileObject(item) as IFile;
        if (isImage(file.name)) {
          return <PreviewImage key={item} path={file.path} active={item === selected} />
        }

        return <CodeEditor key={item} id={item} active={item===selected} />
      })}
    </div>
  </div>
}
