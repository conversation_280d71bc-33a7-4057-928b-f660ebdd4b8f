import React from "react"
import { useSource } from "../context/SourceContext"
import { IFile } from "../types"
import FileIcon from "./FileIcon"
import NavFolderItem from "./NavFolderItem"

interface Props {
  files: IFile[]
  visible: boolean
}

export default function NavFiles({files, visible}: Props) {
  const {setSelect, selected, addOpenedFile} = useSource()
  
  const onShow = async (ev: React.MouseEvent<HTMLDivElement>, file: IFile) => {

    ev.stopPropagation();

    if (file.kind === 'file') {
      setSelect(file.id)
      addOpenedFile(file.id)
    }

  }

  return <div className={`source-codes ${visible ? '' : 'hidden'}`}>
    {files.map(file => {
      const isSelected = file.id === selected;

      if (file.kind === 'directory') {
        return <NavFolderItem active={isSelected} key={file.id} file={file} />
      }

      return <div onClick={(ev) => onShow(ev, file)}
        key={file.id}
        className={`soure-item ${isSelected ? 'source-item-active' : ''} flex items-center gap-3 px-3 py-2 mx-2 rounded-lg cursor-pointer transition-all duration-300 group ${
          isSelected
            ? 'bg-gradient-to-r from-neon-purple/30 to-neon-pink/20 text-white shadow-neon-purple'
            : 'text-gray-400 hover:text-white hover:bg-glass hover:shadow-lg'
        }`}
      >
        <FileIcon name={file.name} />
        <span className="font-medium group-hover:text-neon-purple transition-colors duration-200">{file.name}</span>
        {isSelected && (
          <div className="ml-auto w-2 h-2 rounded-full bg-neon-purple animate-pulse"></div>
        )}
      </div>
    })}
  </div>
}
