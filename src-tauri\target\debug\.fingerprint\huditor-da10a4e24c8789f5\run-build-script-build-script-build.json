{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[10798335591247462934, "build_script_build", false, 3752980630232565117]], "local": [{"RerunIfChanged": {"output": "debug\\build\\huditor-da10a4e24c8789f5\\output", "paths": ["tauri.conf.json"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}