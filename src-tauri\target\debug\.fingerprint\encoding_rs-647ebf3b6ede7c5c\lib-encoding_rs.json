{"rustc": 1842507548689473721, "features": "[\"alloc\", \"default\"]", "declared_features": "[\"alloc\", \"default\", \"fast-big5-hanzi-encode\", \"fast-gb-hanzi-encode\", \"fast-hangul-encode\", \"fast-hanja-encode\", \"fast-kanji-encode\", \"fast-legacy-encode\", \"less-slow-big5-hanzi-encode\", \"less-slow-gb-hanzi-encode\", \"less-slow-kanji-encode\", \"packed_simd\", \"serde\", \"simd-accel\"]", "target": 13561321753160342926, "profile": 15657897354478470176, "path": 13331021440217349926, "deps": [[7216977700954388357, "build_script_build", false, 11384799259982112258], [10411997081178400487, "cfg_if", false, 8242102063645100739]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\encoding_rs-647ebf3b6ede7c5c\\dep-lib-encoding_rs", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}